spring:
  application:
    name: refund-manager-server
  
  datasource:
    url: ***********************************************************************************************************************************************
    username: ${DB_USERNAME:root}
    password: ${DB_PASSWORD:password}
    driver-class-name: com.mysql.cj.jdbc.Driver
  
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQLDialect
        format_sql: true
  
  flyway:
    enabled: true
    locations: classpath:db/migration
    baseline-on-migrate: true
    
server:
  port: 8080
  servlet:
    context-path: /api

logging:
  level:
    com.refundmanager: DEBUG
    org.springframework.web: DEBUG
