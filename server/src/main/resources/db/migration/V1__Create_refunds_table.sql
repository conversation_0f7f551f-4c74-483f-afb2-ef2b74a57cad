CREATE TABLE refunds (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    serial_number VARCHAR(100) NOT NULL,
    product_name VARCHAR(500) NOT NULL,
    product_amount DECIMAL(10,2) NOT NULL,
    has_shipping_insurance BOOLEAN NOT NULL DEFAULT FALSE,
    refund_applied BOOLEAN NOT NULL DEFAULT FALSE,
    shopping_platform VARCHAR(50) NOT NULL,
    refund_date DATE NOT NULL,
    advance_shipping_fee DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    refund_status VARCHAR(20) NOT NULL DEFAULT '未退款',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_refund_status (refund_status),
    INDEX idx_refund_date (refund_date),
    INDEX idx_shopping_platform (shopping_platform),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
